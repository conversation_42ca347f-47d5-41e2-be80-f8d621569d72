import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import MetaTags from "@/components/MetaTags";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import Contact from "@/components/Contact";
import {
  Users,
  Target,
  BookOpen,
  Lightbulb,
  Clock,
  CheckCircle,
  Star,
  ArrowRight,
  MessageCircle,
  TrendingUp,
  Award,
  Heart,
  Zap,
  Calendar,
  PenTool,
  Sparkles,
} from "lucide-react";
import writingCompelling from "@/assets/cover/writingCompelling.webp";
import b1 from "@/assets/brown/b1.webp";

const CoachingForWriters = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [servicesRef, servicesInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [benefitsRef, benefitsInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title = "Writing Coaching Services - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const servicesSpring = useSpring({
    opacity: servicesInView ? 1 : 0,
    transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
  });

  const benefitsSpring = useSpring({
    opacity: benefitsInView ? 1 : 0,
    transform: benefitsInView ? "translateY(0px)" : "translateY(50px)",
  });

  // Course objectives and what students learn
  const courseObjectives = [
    "Craft stories that grab the attention of your audience",
    "Sustain that attention throughout your writing",
    "Communicate your message clearly and compellingly",
    "Leave lasting impressions on your readers",
    "Spur readers to action through compelling storytelling",
    "Write like the writers you admire",
  ];

  const courseFeatures = [
    {
      title: "Story-Based Communication",
      description:
        "Learn to put your thoughts together and communicate them clearly, compellingly through stories.",
      icon: BookOpen,
    },
    {
      title: "Correct Writing Techniques",
      description:
        "Master proper writing techniques, observing necessary punctuation marks and grammar.",
      icon: PenTool,
    },
    {
      title: "Audience Targeting",
      description:
        "Learn how to take a message and tailor it to your specific audience effectively.",
      icon: Target,
    },
    {
      title: "Engaging Content Creation",
      description:
        "Make serious topics interesting to read in blog posts, newsletters, articles, and essays.",
      icon: Sparkles,
    },
  ];

  const classSchedule = {
    frequency: "Thrice a week",
    days: ["Mondays", "Wednesdays", "Saturdays"],
    time: "8 PM to 9 PM each day",
    duration: "4 weeks",
    format: "Online via WhatsApp Course Page",
  };

  const testimonials = [
    {
      name: "Sarah-Sesi-Godonu",
      content:
        "I never knew I would need to sharpen my writing skills. It had never crossed my mind to take a writing class because I thought it was for only aspiring writers. That fateful week, Coach Brown reached out to me, because she felt led to send the information to me when the scholarship application was ongoing. I searched my heart, and I remembered that that same week I was to help my sister reply to her client. I noticed her choice of words and searched my heart again. Then I found out that I really needed to sharpen my writing and communication skills.",
      highlight:
        "Inside you is the most authentic story no one else can write. So I have decided not to hold all of these stories back to myself.",
      rating: 5,
    },
    {
      name: "Bukola Adewuyi",
      content:
        "The course began Feb 12, 2024. My expectation was that the class will take me a step further in fulfilling God's plan for my life by making me a great story teller. After the course: 19th April, 2024. I make bold to see that my expectations have been met and surpassed.",
      highlight:
        "I learnt that the basic tactic to present my message is with a STORY. Stories just have a way of keeping people hooked.",
      rating: 5,
    },
    {
      name: "Olamilekan Adenusi",
      content:
        "The Course began February 12, 2024. And I was expecting to learn how to effectively pen down all that my mind saw, pondered, and reflected upon. I had no idea what you would be teaching us when I started this course—except that I should know how to write compelling stories in the end. It's April 15.",
      highlight:
        "And I've learnt so much that I now feel confident when writing. I feel like a writer. I'm beginning to think more like a writer.",
      rating: 5,
    },
    {
      name: "Mercy Adegbenro",
      content:
        "The course began February 12, 2024. My expectation was to be able to express myself better through writing and also that it reflects in my oral communication. After the course: March 9, 2024. I have surpassed my expectations.",
      highlight:
        "Coach did not stop at helping me express myself better through writing but broke things down and made me understand why I am writing the story, who my audience is, the feelings and emotions put into writing.",
      rating: 5,
    },
    {
      name: "Timilehin Oyinloye",
      content:
        "April 30, 2023. At first, when I enrolled into this class, I didn't really have any purpose of joining then. I knew I was lacking in one aspect or the other in terms of punctuations and the rest. Thank God, now I can say that I'm better.",
      highlight:
        "When I joined this class, you were able to open my eyes to the use of commas, letting me know the places where I was using them unnecessarily.",
      rating: 5,
    },
    {
      name: "Surprise Zihlavski",
      content:
        "By the end of the course, I had hoped to gather enough courage to pursue my dream of becoming an author. I am happy to say that I have achieved more than that. Now, I not only have the courage but also the knowledge to begin my journey.",
      highlight:
        "I used to believe I lacked the creativity to write a fictional book, but now, with all I have learned, the only limit is myself.",
      rating: 5,
    },
  ];

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <>
      <MetaTags
        title="Writing Coaching Services - Brown Patience | Professional Writing Coach"
        description="Transform your writing with personalized coaching from Brown Patience. Develop your voice, improve your craft, and achieve your writing goals with expert guidance."
        image="/og-images/CoachingForWriters.webp"
        url="https://thebrownpatiencecompany.com.ng/services/coaching-for-writers"
        keywords="writing coaching, writing mentor, author coaching, writing skills development, Brown Patience coaching"
      />
      <div className="min-h-screen bg-cream-50 ">
        {/* Hero Section */}
        <section ref={heroRef} className="py-20 hero-accent-bg">
          <div className="container mx-auto px-4">
            <animated.div style={heroSpring} className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Text Content */}
                <div className="text-center lg:text-left">
                  <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-6">
                    <Users className="w-4 h-4" />
                    Writing Coaching & Mentorship
                  </div>
                  <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
                    Coaching For Writers
                  </h1>
                  <p className="text-xl text-brand-secondary/70 mb-8 leading-relaxed">
                    The sure fire way to get attention with your writing is to
                    tell a story. Because stories keep people hooked. And this
                    writing class teaches you to tell compelling stories in your
                    writing.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      variant="hero"
                      size="lg"
                      onClick={scrollToContact}
                      className="group"
                    >
                      Start Your Journey
                      <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
                    </Button>
                    <Button variant="outline" size="lg">
                      Free Discovery Call
                    </Button>
                  </div>
                </div>

                {/* Hero Image */}
                <div className="flex justify-center lg:justify-end">
                  <div className=" rounded-lg h-full w-full max-w-md flex items-center justify-center shadow-lg shadow-brand-accent-light">
                    <img
                      src={b1}
                      alt="brown patience"
                      className="h-full w-full object-cover rounded-lg"
                    />
                  </div>
                </div>
              </div>
            </animated.div>
          </div>
        </section>

        {/* Overview Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-8">
                  The Art of Writing Compelling Stories
                </h2>
              </div>

              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Overview Content */}
                <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant">
                  <p className="text-lg text-brand-secondary/80 leading-relaxed mb-6">
                    Stories. We love them. We read them. We pay attention to
                    them. And if you can tell a compelling story, you can get
                    our attention, and you can move us. You can move us to give
                    attention to our families, to protect the environment, to
                    buy a product, to save our own lives. I am a story writing
                    coach who teaches you to tell stories that move. In a 4-week
                    course called The Art of Writing Compelling Stories.
                  </p>
                  <blockquote className="text-lg text-brand-secondary/80 leading-relaxed italic border-l-4 border-brand-accent pl-6">
                    "I learnt that the basic tactic to present my message is
                    with a STORY. Stories just have a way of keeping people
                    hooked. No wonder I can spend a whole day reading a 428-page
                    Christian fiction book and yet start yawning the moment I
                    pick up a research article." - Bukola Adewuyi
                  </blockquote>
                </div>

                {/* Overview Image */}
                <div className="flex justify-center">
                  <div className=" rounded-lg h-full w-full flex items-center justify-center border-2 border-dashed border-brand-accent/30">
                    <img
                      src={writingCompelling}
                      alt=""
                      className="h-full w-full object-cover rounded-lg"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Course Objectives Section */}
        <section className="py-20 bg-brand-neutral/30">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                  Course Objectives
                </h2>
                <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                  At the end of this Course, you would have learnt how to craft
                  stories that grab the attention of your audience, sustain that
                  attention, and communicate your message.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6 mb-12">
                {courseObjectives.map((objective, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-4 p-6 bg-brand-primary rounded-lg"
                  >
                    <div className="w-8 h-8 bg-brand-accent/10 rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-4 h-4 text-brand-accent" />
                    </div>
                    <span className="text-brand-secondary font-medium">
                      {objective}
                    </span>
                  </div>
                ))}
              </div>

              <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant">
                <p className="text-lg text-brand-secondary/80 leading-relaxed mb-6">
                  At the end of this 4-week course, you'll be a better writer.
                  You'll come to recognize just how good writers write. Your
                  eyes will be opened to note what makes their writing so
                  effective. It'll be like looking at a slice of cake and
                  finally saying, "Hey, I know how you baked that!"
                </p>
                <p className="text-lg text-brand-secondary/80 leading-relaxed">
                  We love stories. We read them. We pay attention to them. If
                  you want to tell us something, tell us through a story. You'll
                  get our attention. And if you can tell a compelling story, you
                  can move us.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* What We Do Section */}
        <section ref={servicesRef} className="py-20">
          <div className="container mx-auto px-4">
            <animated.div style={servicesSpring}>
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                  What We Do in This Writing Class
                </h2>
                <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                  Comprehensive training to help you communicate clearly and
                  compellingly through storytelling.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                {courseFeatures.map((feature, index) => {
                  const IconComponent = feature.icon;
                  return (
                    <animated.div
                      key={feature.title}
                      style={{
                        opacity: servicesInView ? 1 : 0,
                        transform: servicesInView
                          ? "translateY(0px)"
                          : "translateY(50px)",
                        transitionDelay: servicesInView
                          ? `${200 + index * 100}ms`
                          : "0ms",
                        transition: "all 0.6s ease-out",
                      }}
                    >
                      <Card className="h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                        <CardHeader>
                          <div className="flex items-center gap-4 mb-4">
                            <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center">
                              <IconComponent className="w-6 h-6 text-brand-accent" />
                            </div>
                            <h3 className="text-xl font-serif font-bold text-brand-secondary">
                              {feature.title}
                            </h3>
                          </div>
                          <p className="text-brand-secondary/70 leading-relaxed">
                            {feature.description}
                          </p>
                        </CardHeader>
                      </Card>
                    </animated.div>
                  );
                })}
              </div>
            </animated.div>
          </div>
        </section>

        {/* How Classes Hold Section */}
        <section ref={benefitsRef} className="py-20 bg-brand-neutral/30">
          <div className="container mx-auto px-4">
            <animated.div style={benefitsSpring}>
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                  How Do Classes Hold?
                </h2>
                <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                  Classes hold entirely online. On a WhatsApp Course Page. Each
                  class is followed by writing submissions (assignments).
                </p>
              </div>

              <div className="max-w-4xl mx-auto">
                <div className="grid md:grid-cols-2 gap-8 mb-12">
                  <Card className="hover:shadow-elegant transition-all duration-300">
                    <CardHeader>
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center">
                          <Calendar className="w-6 h-6 text-brand-accent" />
                        </div>
                        <h3 className="text-xl font-serif font-bold text-brand-secondary">
                          Class Schedule
                        </h3>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-brand-secondary/70">
                            Frequency:
                          </span>
                          <span className="font-medium text-brand-secondary">
                            {classSchedule.frequency}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-brand-secondary/70">Days:</span>
                          <span className="font-medium text-brand-secondary">
                            {classSchedule.days.join(", ")}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-brand-secondary/70">Time:</span>
                          <span className="font-medium text-brand-secondary">
                            {classSchedule.time}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-brand-secondary/70">
                            Duration:
                          </span>
                          <span className="font-medium text-brand-secondary">
                            {classSchedule.duration}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-brand-secondary/70">
                            Format:
                          </span>
                          <span className="font-medium text-brand-secondary">
                            {classSchedule.format}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="hover:shadow-elegant transition-all duration-300">
                    <CardHeader>
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center">
                          <Award className="w-6 h-6 text-brand-accent" />
                        </div>
                        <h3 className="text-xl font-serif font-bold text-brand-secondary">
                          Certification & Mentorship
                        </h3>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-brand-secondary/70 leading-relaxed mb-4">
                        After 4 weeks, students who have completed the course
                        work (their writing submissions) will be certified.
                      </p>
                      <p className="text-brand-secondary/70 leading-relaxed">
                        And mentorship follows from there. Mentorship means that
                        students can continue to relate with the Coach, ask
                        questions, seek her input, and count on her guidance in
                        their writing journey.
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant text-center">
                  <h3 className="text-2xl font-serif font-bold text-brand-secondary mb-4">
                    How Do You Join the Story Writing Class?
                  </h3>
                  <div className="grid md:grid-cols-3 gap-6 mb-8">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-brand-primary font-bold">1</span>
                      </div>
                      <p className="text-brand-secondary font-medium">
                        Find out when the next edition begins
                      </p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-brand-primary font-bold">2</span>
                      </div>
                      <p className="text-brand-secondary font-medium">
                        Pay the course fee
                      </p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-brand-primary font-bold">3</span>
                      </div>
                      <p className="text-brand-secondary font-medium">
                        Get ready for class
                      </p>
                    </div>
                  </div>
                  <p className="text-brand-secondary/70 mb-6">
                    Want to know when the next edition begins? Please send a
                    message below.
                  </p>
                  <Button
                    variant="hero"
                    size="lg"
                    onClick={scrollToContact}
                    className="group"
                  >
                    Get Started Today
                    <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </div>
            </animated.div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Testimonials From Students
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                The 4-week online writing class began in 2021, and there are
                more testimonials than I can possibly upload. You'll find a
                small percentage below.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <Card
                  key={testimonial.name}
                  className="hover:shadow-elegant transition-all duration-300"
                >
                  <CardContent className="p-6">
                    <div className="flex items-center gap-1 mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star
                          key={i}
                          className="w-4 h-4 fill-brand-accent text-brand-accent"
                        />
                      ))}
                    </div>
                    {testimonial.highlight && (
                      <blockquote className="text-brand-accent font-medium italic mb-4 border-l-4 border-brand-accent pl-4">
                        "{testimonial.highlight}"
                      </blockquote>
                    )}
                    <p className="text-brand-secondary/80 mb-4 text-sm leading-relaxed">
                      {testimonial.content.length > 200
                        ? `${testimonial.content.substring(0, 200)}...`
                        : testimonial.content}
                    </p>
                    <div>
                      <p className="font-semibold text-brand-secondary">
                        {testimonial.name}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <Contact
          title="Ready to Join The Art of Writing Compelling Stories?"
          subtitle="Get Started Today"
          description="Want to know when the next edition begins? Send me a message and I'll get back to you with all the details about the upcoming class."
          whatsappMessage="Hi Coach Brown! I'm interested in joining The Art of Writing Compelling Stories class. Could you please let me know when the next edition begins and provide me with more details?"
          backgroundColor="bg-brand-neutral/30"
        />
      </div>
    </>
  );
};

export default CoachingForWriters;
