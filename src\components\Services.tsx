import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  ArrowRight,
  BookOpen,
  Users,
  PenTool,
  MessageCircle,
  FileText,
  Video,
} from "lucide-react";

interface Service {
  id: string;
  title: string;
  description: string;
  features: string[];
  icon: React.ComponentType<{ className?: string }>;
  price: string;
  duration: string;
  ctaText: string;
  popular?: boolean;
}

const services: Service[] = [
  {
    id: "1",
    title: "Book Writing & Editing",
    description:
      "Have professional eyes go through your manuscript or get help putting your thoughts together to create a book.",
    features: [
      "Manuscript development and structuring",
      "Professional editing and proofreading",
      "Character and plot development",
      "Publishing guidance and support",
      "Collaborative writing process",
    ],
    icon: BookOpen,
    price: "Contact",
    duration: "for pricing",
    ctaText: "Learn More",
    popular: true,
  },
  {
    id: "2",
    title: "Content Writing",
    description:
      "Social media content, web copies, newsletters, blog posts — tell me what you need, and you'll get it.",
    features: [
      "Social media content creation",
      "Website copy and blog posts",
      "Newsletter writing",
      "Marketing materials",
      "Brand voice development",
    ],
    icon: PenTool,
    price: "Contact",
    duration: "for pricing",
    ctaText: "Get Started",
  },
  {
    id: "3",
    title: "Coaching For Writers",
    description:
      "Learn to tell compelling stories that keep people hooked through our writing class.",
    features: [
      "Storytelling techniques",
      "Writing craft development",
      "Narrative structure training",
      "Character development skills",
      "Publishing readiness preparation",
    ],
    icon: Users,
    price: "Contact",
    duration: "for pricing",
    ctaText: "Join Class",
  },
];

const Services = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <section id="services" ref={ref} className="py-20 bg-brand-neutral/30">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Users className="w-4 h-4" />
              My Services
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              My Services
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto mb-6">
              "If you have a great message or story to share. . . reach Brown
              Patience." — Timi Oshinowo.
            </p>
          </div>

          {/* Services Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => {
              const IconComponent = service.icon;

              return (
                <animated.div
                  key={service.id}
                  style={{
                    opacity: inView ? 1 : 0,
                    transform: inView ? "translateY(0px)" : "translateY(50px)",
                    transitionDelay: inView ? `${200 + index * 100}ms` : "0ms",
                    transition: "all 0.6s ease-out",
                  }}
                >
                  <Card
                    className={`group h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 interactive-lift relative ${
                      service.popular ? "ring-2 ring-brand-accent/20" : ""
                    }`}
                  >
                    {service.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-brand-accent text-brand-primary px-4 py-1 rounded-full text-xs font-medium">
                          Most Popular
                        </span>
                      </div>
                    )}

                    <CardHeader className="text-center">
                      <div className="mx-auto w-16 h-16 bg-brand-accent/10 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-brand-accent/20 transition-colors">
                        <IconComponent className="w-8 h-8 text-brand-accent" />
                      </div>
                      <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                        {service.title}
                      </h3>
                      <p className="text-brand-secondary/70 leading-relaxed">
                        {service.description}
                      </p>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <div className="space-y-6">
                        {/* Features */}
                        <div className="space-y-3">
                          {service.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-brand-accent rounded-full mt-2 flex-shrink-0"></div>
                              <span className="text-sm text-brand-secondary/80">
                                {feature}
                              </span>
                            </div>
                          ))}
                        </div>

                        {/* Pricing */}
                        <div className="border-t border-brand-grey/30 pt-6">
                          <div className="text-center mb-4">
                            <div className="text-2xl font-bold text-brand-secondary">
                              {service.price}
                            </div>
                            <div className="text-sm text-brand-secondary/60">
                              {service.duration}
                            </div>
                          </div>

                          <Button
                            variant={service.popular ? "hero" : "outline"}
                            className="w-full group"
                            onClick={scrollToContact}
                          >
                            {service.ctaText}
                            <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </animated.div>
              );
            })}
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <div className="bg-brand-primary rounded-2xl p-8 shadow-elegant max-w-2xl mx-auto">
              <h3 className="text-2xl font-serif font-bold text-brand-secondary mb-4">
                Not sure which service is right for you?
              </h3>
              <p className="text-brand-secondary/70 mb-6">
                Schedule a free 15-minute consultation to discuss your writing
                goals and find the perfect fit.
              </p>
              <Button
                variant="hero"
                size="lg"
                onClick={scrollToContact}
                className="group"
              >
                Book Free Consultation
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default Services;
