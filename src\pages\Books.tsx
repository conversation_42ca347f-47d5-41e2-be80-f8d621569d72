import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import MetaTags from "@/components/MetaTags";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { BookOpen, ExternalLink } from "lucide-react";
import Contact from "@/components/Contact";
import priye from "@/assets/cover/priye.webp";
import Chimamanda from "@/assets/cover/chimamanda.webp";
import chooseYourThoughts from "@/assets/cover/chooseYourThoughts.webp";
import soHeTaught from "@/assets/cover/soHeTaught.webp";
import adeni from "@/assets/cover/adeni.webp";
import fantasy from "@/assets/cover/fantasy.webp";
import bodyWork from "@/assets/cover/bodyWork.webp";
import soHeTaughtMeNo from "@/assets/cover/soHeTaughtMeNo.webp";

const Books = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [booksRef, booksInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title = "My Books - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const booksSpring = useSpring({
    opacity: booksInView ? 1 : 0,
    transform: booksInView ? "translateY(0px)" : "translateY(30px)",
  });

  const books = [
    {
      id: 1,
      title: "Fantasy",
      description:
        "Fantasy is about the sexual mental struggles we face. Those steamy thoughts that feel like you're engaging in a porn video production. Fantasy is for the young woman who'd like her thoughts to honor God, who'd like to bring her thoughts under the Holy Spirit's control.\n\nIt won't just be that you're not having premarital sex, but were we to play the content of your mind on a screen, you'd have no cause to be ashamed. That is the \"why\" of this book. Written by a girl to another girl.",
      coverUrl: fantasy,
      buyLink: "https://selar.co/FANTASY", // Replace with actual link
    },
    {
      id: 2,
      title: "Chimamanda",
      description:
        "Born to a man without warmth, Chimamanda is married off early to a man she dreads just as much as her father. But her walls are in place; she is safe — as long as she stays strong.\n\nShe would have been content living as she had always lived. But God has His way of squeezing out what life you thought you had so He can give you true life. So He can heal and make you free.",
      coverUrl: Chimamanda,
      buyLink: "https://selar.co/chimamanda_BPM", // Replace with actual link
    },
    {
      id: 3,
      title: "Choose your Thoughts",
      description:
        "What you do with your mind will turn and do you. I should know. If you brood and ruminate on depressing thoughts, you'll be depressed. I should know. Because on Tuesday, April 5, 2023, I was contemplating suicide. Thoughts, thoughts led me there.\n\nI do an awful lot of cooking—mental cooking. You do too. Now, here's a chance to be sure you're cooking the right things. If you change your mind, it will change your life. As literally as you'll see in this book.",
      coverUrl: chooseYourThoughts,
      buyLink: "https://selar.co/ChooseYourThoughts", // Replace with actual link
    },
    {
      id: 4,
      title: "So He Taught Me 'No'",
      description:
        "This is a book about porn addiction. About the darkness that persists even after you've stopped viewing it. All based on true experience. It's a book about mind renewal. A book about how the Holy Spirit teaches you to say \"No\" to ungodliness — no matter how strong the mental stronghold.\n\nIt shows you that addictions are not stronger than the God who sets free.",
      coverUrl: soHeTaughtMeNo,
      buyLink: "https://selar.co/SHTM-NO", // Replace with actual link
    },
    {
      id: 5,
      title: "Priye",
      description:
        "Priye is another bright high school student. Never had it been heard that students so young and privileged could be involved in such vices. But they weren't just involved, they were drowning.\n\nThis is a tale of the burdens words cannot describe, the one that feels like it's crushing your chest. It's a tale about the rest that is found in surrender. The rest Jesus gives.",
      coverUrl: priye,
      buyLink: "https://selar.co/Priye", // Replace with actual link
    },
    {
      id: 6,
      title: "Adeni - A StrongHold Broken",
      description:
        "Raised in a markedly dysfunctional background, Adeni learned to cope, to survive, to be strong for the ones who needed her. And like a fortified city, she built a wall around herself. It kept the pain away.\n\nYet little by little, a seemingly losing battle, light is seeping into this walled city. Despite the resistance of unbelief, beyond the panic of change, freedom dawns.",
      coverUrl: adeni,
      buyLink: "https://selar.co/SHTM-NO", // Replace with actual link
    },
    {
      id: 6,
      title: "Body Work",
      description:
        "Girlfriend, a book for us. About how we perceive our bodies. Those evolving standards that have you wishing you were slim one moment and thick the next. The image that Holly and all the other Woods have told us is the perfect body type. The mental strongholds that have us living in the most gripping fears about our future. A book about a topic that I—and at least one other girl—have struggled with. Fiercely. Since my teenage years. A book my friend says is really needed. It's not for sale. It's for you and the girl next door.",
      coverUrl: bodyWork,
      buyLink: "https://selar.com/bodywork", // Replace with actual link
    },
  ];

  const handleBuyClick = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  return (
    <>
      <MetaTags
        title="Books by Brown Patience - Published Author"
        description="Explore the published works of Brown Patience. Discover powerful stories and insights that inspire and transform lives through compelling storytelling."
        image="/og-images/books.webp"
        url="https://thebrownpatiencecompany.com.ng/books"
        keywords="Brown Patience books, published author, Christian fiction, inspirational books, transformational stories"
      />
      <div className="min-h-screen bg-cream-50">
        {/* Hero Section */}
        <animated.section
          ref={heroRef}
          style={heroSpring}
          className="py-20 px-4 sm:px-6 lg:px-8 bg-hero-accent-gradient"
        >
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Text Content */}
              <div className="text-center lg:text-left">
                <div className="inline-flex items-center gap-2 bg-brown-100 text-brown-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                  <BookOpen className="w-4 h-4" />
                  My Books
                </div>
                <h1 className="text-4xl md:text-6xl font-serif text-brown-900 mb-6">
                  My BOOKS
                </h1>
                <p className="text-xl text-brown-700 leading-relaxed">
                  Some of these books have been described as brutally
                  honest—with regard to the I'll-tell-it-as-it-is description of
                  addiction. Reviews have been utterly intimate, held safe in my
                  heart.
                </p>
              </div>

              {/* Hero Image */}
              <div className="flex justify-center lg:justify-end">
                <div className="relative">
                  <img
                    src={soHeTaught}
                    alt="So He Taught Me No book cover"
                    className="w-[30rem] h-auto rounded-lg shadow-elegant hover:scale-105 transition-transform duration-300"
                    loading="eager"
                  />
                </div>
              </div>
            </div>
          </div>
        </animated.section>

        {/* Books Grid */}
        <animated.section
          ref={booksRef}
          style={booksSpring}
          className="py-16 px-4 sm:px-6 lg:px-8"
        >
          <div className="max-w-6xl mx-auto">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {books.map((book, index) => (
                <animated.div
                  key={book.id}
                  style={{
                    opacity: booksInView ? 1 : 0,
                    transform: booksInView
                      ? "translateY(0px)"
                      : "translateY(50px)",
                    transitionDelay: booksInView
                      ? `${200 + index * 100}ms`
                      : "0ms",
                    transition: "all 0.6s ease-out",
                  }}
                >
                  <Card className="h-full hover:shadow-lg transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 group">
                    <div className="aspect-[3/4] overflow-hidden rounded-t-lg">
                      <img
                        src={book.coverUrl}
                        alt={`${book.title} cover`}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        loading="lazy"
                      />
                    </div>
                    <CardHeader>
                      <h3 className="text-xl font-serif text-brown-900 mb-3 group-hover:text-brown-700 transition-colors">
                        {book.title}
                      </h3>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-brown-600 mb-6 leading-relaxed text-sm">
                        {book.description.split("\n\n")[0]}...
                      </p>
                      <Button
                        onClick={() => handleBuyClick(book.buyLink)}
                        className="w-full bg-brown-800 hover:bg-brown-900 bg-brand-accent text-white group/btn"
                      >
                        GET YOUR COPY
                        <ExternalLink className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                      </Button>
                    </CardContent>
                  </Card>
                </animated.div>
              ))}
            </div>
          </div>
        </animated.section>

        {/* Contact Section */}
        <Contact
          title="Ready to Transform Your Mind?"
          subtitle="Get in Touch"
          description="Have questions about any of these books or need guidance on your journey to mental freedom? I'm here to help."
          backgroundColor="bg-brown-50"
        />
      </div>
    </>
  );
};

export default Books;
