import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import MetaTags from "@/components/MetaTags";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import Contact from "@/components/Contact";
import Appointment from "@/components/Appointment";
import editingData from "@/data/editingData";
import {
  BookOpen,
  Edit3,
  FileText,
  CheckCircle,
  Clock,
  Users,
  Star,
  ArrowRight,
  PenTool,
  Target,
  Award,
  MessageCircle,
  Mic,
  UserCheck,
  Quote,
} from "lucide-react";

// Portfolio images
import educellImg from "@/assets/cover/edusell.webp";
import deborahImg from "@/assets/cover/deborah.webp";
import envisionImg from "@/assets/cover/envision.webp";
import onpointImg from "@/assets/cover/onPoint.webp";
import billionaireImg from "@/assets/cover/billionaire.webp";
import battlesImg from "@/assets/cover/battles.webp";
import b8 from "@/assets/brown/b8.webp";
import editingHero from "@/assets/cover/editingHero.webp";

const BookWritingEditing = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [servicesRef, servicesInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [processRef, processInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title =
      "Book Writing & Editing Services - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const servicesSpring = useSpring({
    opacity: servicesInView ? 1 : 0,
    transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
  });

  const processSpring = useSpring({
    opacity: processInView ? 1 : 0,
    transform: processInView ? "translateY(0px)" : "translateY(50px)",
  });

  const editingServices = [
    {
      title: "Editing",
      description:
        "You need me to dot your I's, cross your T's and ensure no grammatical or typographic errors survive. As your editor, I serve as a professional pair of eyes who will iron out mistakes and polish your writing until it glimmers.",
      icon: Edit3,
    },
    {
      title: "Ghost Writing",
      description:
        "You know what you want to write, but you probably only have the skeletal framework. It's still in your head, scribbled on several notepads, or scattered round your laptop. I come around to understand precisely what you want to write and write it out for you. It could be that you just don't have the time to write. Therefore, you share your dream with me and I reproduce it on paper. Clearly, compellingly.",
      icon: PenTool,
    },
    {
      title: "Collaboration",
      description:
        "You have an existing piece of work. With collaboration, a paragraph could be redone or deleted altogether. A better introduction could be added, repetitions removed, and the message better explained. Collaboration is when I join with you to make your message as rich as it should be. It's a partnership.",
      icon: Users,
    },
    {
      title: "Transcription",
      description:
        "What you have is a recording—audio or video recording—and you want that recording made into a book. First, I transcribe that recording, and then I take it from there, making it into a book. It's perfect for people who'd rather 'talk' their thoughts or message than sit to type it out.",
      icon: Mic,
    },
  ];

  const writingProcess = [
    {
      step: "1",
      title: "Meeting You & the Vision",
      description:
        "This is the very first interaction where you and I get to talk. You send me your draft for editing or collaboration, audio or video files for transcription, or explain what you want to write if you need the ghostwriting service. As we talk over the phone or interact via text, you help me understand who you are and what you want to say, what service you need, and how soon you want it done.",
      icon: MessageCircle,
    },
    {
      step: "2",
      title: "Finding Clarity",
      description:
        "I'll ask questions as we talk so we can clearly articulate your message, define your audience, and see if there is a need to return to the drawing board. Reviewing your manuscript or listening as you explain what you want to write gives us a chance to determine just how defined your message is.",
      icon: FileText,
    },
    {
      step: "3",
      title: "Writing & Editing",
      description:
        "When we're clear on precisely what you want, work will commence. This phase is where the actual writing takes place. Using an outline we designed, your book will come together seamlessly, with you reserving the right to monitor each milestone, ensuring it captures your voice and your 'Why?' You get to approve the work as we go.",
      icon: Edit3,
    },
    {
      step: "4",
      title: "Completion & Approval",
      description:
        "Now it's all done. When the work is completed and approved, your book, or any other written project, is fully yours. You hold in your hand the very thing you've been longing to write. This is the point where you happily promise to refer me to everyone who'll need this service.",
      icon: Award,
    },
  ];

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <>
      <MetaTags
        title="Book Writing & Editing Services - Brown Patience | Professional Author Services"
        description="Professional book writing and editing services to help you craft compelling stories and share your message with the world. Expert ghostwriting, editing, and manuscript development."
        image="/og-images/BookWritingEditing.webp"
        url="https://thebrownpatiencecompany.com.ng/services/book-writing-editing"
        keywords="book writing services, professional editing, ghostwriting, manuscript development, book editing, Brown Patience services"
      />
      <div className="min-h-screen bg-cream-50">
        {/* Hero Section */}
        <section ref={heroRef} className="py-20 hero-accent-bg">
          <div className="container mx-auto px-4">
            <animated.div style={heroSpring} className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Text Content */}
                <div className="text-center lg:text-left">
                  <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-6">
                    <BookOpen className="w-4 h-4" />
                    Professional Writing & Editing Services
                  </div>
                  <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
                    Book Writing & Editing
                  </h1>
                  <p className="text-xl text-brand-secondary/70 mb-8 leading-relaxed">
                    Mine is to ensure you write that important book, that you
                    share that necessary message. And the ways I help you are
                    editing, collaboration, ghostwriting, and transcription.
                    From developmental editing to final proofreading, I provide
                    comprehensive services that honor your unique voice while
                    ensuring professional quality.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      variant="hero"
                      size="lg"
                      onClick={scrollToContact}
                      className="group"
                    >
                      Start Your Project
                      <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
                    </Button>
                    <Button variant="outline" size="lg">
                      View Portfolio
                    </Button>
                  </div>
                </div>

                {/* Hero Image */}
                <div className="flex justify-center lg:justify-end">
                  <div className="rounded-lg h-full w-full max-w-md flex items-center justify-center">
                    <img
                      src={editingHero}
                      alt="note and pen"
                      className="h-[30rem] w-full object-cover object-top rounded-lg"
                    />
                  </div>
                </div>
              </div>
            </animated.div>
          </div>
        </section>

        {/* Services Section */}
        <section ref={servicesRef} className="py-20">
          <div className="container mx-auto px-4">
            <animated.div style={servicesSpring}>
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                  What Service Do You Need?
                </h2>
                <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                  "Okay, I'm in. How do we do this?" I hear you ask. Here are
                  the ways I can help you bring your message to life through the
                  written word.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                {editingServices.map((service, index) => {
                  const IconComponent = service.icon;
                  return (
                    <animated.div
                      key={service.title}
                      style={{
                        opacity: servicesInView ? 1 : 0,
                        transform: servicesInView
                          ? "translateY(0px)"
                          : "translateY(50px)",
                        transitionDelay: servicesInView
                          ? `${200 + index * 100}ms`
                          : "0ms",
                        transition: "all 0.6s ease-out",
                      }}
                    >
                      <Card className="h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                        <CardHeader>
                          <div className="flex items-center gap-4 mb-4">
                            <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center">
                              <IconComponent className="w-6 h-6 text-brand-accent" />
                            </div>
                            <div>
                              <h3 className="text-xl font-serif font-bold text-brand-secondary">
                                {service.title}
                              </h3>
                            </div>
                          </div>
                          <p className="text-brand-secondary/70 leading-relaxed">
                            {service.description}
                          </p>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div className="flex items-start gap-3">
                              <CheckCircle className="w-4 h-4 text-brand-accent mt-0.5 flex-shrink-0" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </animated.div>
                  );
                })}
              </div>
            </animated.div>
          </div>
        </section>

        {/* Process Section */}
        <section ref={processRef} className="py-20 bg-hero-accent-gradient">
          <div className="container mx-auto px-4">
            <animated.div style={processSpring}>
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                  My Writing & Editing Process
                </h2>
                <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                  A collaborative approach that ensures your vision comes to
                  life while maintaining the highest standards.
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {writingProcess.map((step, index) => {
                  const IconComponent = step.icon;
                  return (
                    <animated.div
                      key={step.step}
                      style={{
                        opacity: processInView ? 1 : 0,
                        transform: processInView
                          ? "translateY(0px)"
                          : "translateY(50px)",
                        transitionDelay: processInView
                          ? `${200 + index * 100}ms`
                          : "0ms",
                        transition: "all 0.6s ease-out",
                      }}
                      className="text-center"
                    >
                      <div className="relative mb-6">
                        <div className="w-16 h-16 bg-brand-accent rounded-full flex items-center justify-center mx-auto mb-4">
                          <IconComponent className="w-8 h-8 text-brand-primary" />
                        </div>
                        <div className="absolute -top-2 -right-2 w-8 h-8 bg-brand-secondary text-brand-primary rounded-full flex items-center justify-center text-sm font-bold">
                          {step.step}
                        </div>
                      </div>
                      <h3 className="text-lg font-serif font-bold text-brand-secondary mb-3">
                        {step.title}
                      </h3>
                      <p className="text-brand-secondary/70 text-sm leading-relaxed">
                        {step.description}
                      </p>
                    </animated.div>
                  );
                })}
              </div>
            </animated.div>
          </div>
        </section>

        {/* Portfolio Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Few Books I've Worked On
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                Here are some of the projects I've had the privilege to work on,
                each one a unique journey of bringing powerful messages to life.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Edusell */}
              <Card className="hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                <CardHeader className="p-0">
                  <div className="aspect-[3/4] rounded-t-lg overflow-hidden">
                    <img
                      src={educellImg}
                      alt="Edusell book cover"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                    Edusell
                  </h3>
                  <p className="text-brand-secondary/70 text-sm leading-relaxed mb-4">
                    When this manuscript got to me, I was told it had been
                    edited already. I was glad it hadn't been published in that
                    state. Cause it "needed" work.
                  </p>
                  <div className="space-y-2 text-sm text-brand-secondary/70">
                    <div className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-brand-accent mt-0.5 flex-shrink-0" />
                      <span>Grouping of similar parts into sections</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-brand-accent mt-0.5 flex-shrink-0" />
                      <span>Rearranging chapters for better flow</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-brand-accent mt-0.5 flex-shrink-0" />
                      <span>Renaming chapters for clarity</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* A Gift To Deborah */}
              <Card className="hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                <CardHeader className="p-0">
                  <div className="aspect-[3/4] rounded-t-lg overflow-hidden">
                    <img
                      src={deborahImg}
                      alt="A Gift To Deborah book cover"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                    A Gift To Deborah
                  </h3>
                  <p className="text-brand-secondary/70 text-sm leading-relaxed mb-4">
                    Precious Ayomikun carries the girl child in her thoughts.
                    When she decided to write a book for young women, detailing
                    her own stroll through the corridors of depression and
                    inadequacy, I was glad to collaborate with her.
                  </p>
                  <p className="text-brand-secondary/70 text-sm italic">
                    "Wherever a young woman finds this book, it'll be a gift
                    indeed."
                  </p>
                </CardContent>
              </Card>

              {/* Envision */}
              <Card className="hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                <CardHeader className="p-0">
                  <div className="aspect-[3/4] rounded-t-lg overflow-hidden">
                    <img
                      src={envisionImg}
                      alt="Envision book cover"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                    Envision
                  </h3>
                  <p className="text-brand-secondary/70 text-sm leading-relaxed">
                    This Nigerian-born medical doctor could have been
                    prescribing medication only for the body, but she decided to
                    craft medicine for the mind as well. Doctor Oyindamola wrote
                    a short, precise, and sharp book targeted at the ones who
                    want to live purposefully.
                  </p>
                </CardContent>
              </Card>

              {/* On Point */}
              <Card className="hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                <CardHeader className="p-0">
                  <div className="aspect-[3/4] rounded-t-lg overflow-hidden">
                    <img
                      src={onpointImg}
                      alt="On Point book cover"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                    On Point
                  </h3>
                  <p className="text-brand-secondary/70 text-sm leading-relaxed">
                    Days after its author released this pointed missive into the
                    world, the number of downloads and reviews assured me afresh
                    of what I'd known since I saw the manuscript: Olamide wrote
                    a necessary book.
                  </p>
                </CardContent>
              </Card>

              {/* Billionaire Codes */}
              <Card className="hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                <CardHeader className="p-0">
                  <div className="aspect-[3/4] rounded-t-lg overflow-hidden">
                    <img
                      src={billionaireImg}
                      alt="Billionaire Codes book cover"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                    Billionaire Codes
                  </h3>
                  <p className="text-brand-secondary/70 text-sm leading-relaxed">
                    The personnel in charge of getting Dr. Stephen Akintayo's
                    Billionaire Codes edited sought an editor who could cut out
                    repetitions, tighten every chapter, and sharpen the message.
                    It was my honor to serve as the editor they needed.
                  </p>
                </CardContent>
              </Card>

              {/* 7 Battles Every Trader Must Fight to Win */}
              <Card className="hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                <CardHeader className="p-0">
                  <div className="aspect-[3/4] rounded-t-lg overflow-hidden">
                    <img
                      src={battlesImg}
                      alt="7 Battles Every Trader Must Fight to Win book cover"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                    7 Battles Every Trader Must Fight to Win
                  </h3>
                  <p className="text-brand-secondary/70 text-sm leading-relaxed">
                    A cryptocurrency expert, a coach to many, Mayowa Owolabi
                    certainly knows the struggles of traders. When he needed the
                    videos from his coaching sessions transcribed into this
                    book, I was glad to make the dream a hardcover reality.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Philosophy Section */}
        <section className="py-20 bg-brand-neutral/30">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Philosophy Content */}
                <div>
                  <h2 className="text-3xl md:text-4xl md:text-start text-center font-serif font-bold text-brand-secondary mb-8">
                    Why Writing Matters
                  </h2>
                  <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant">
                    <p className="text-lg text-brand-secondary/80 leading-relaxed mb-6 italic">
                      "I consider writing a change-provoking art. Books have
                      molded my life and the lives of many before now. Every
                      person who has ever had anything to say has encouraged us
                      to glean the wisdom in books."
                    </p>
                    <p className="text-lg text-brand-secondary/80 leading-relaxed">
                      Writing is a change-provoking art. It's why we read. It's
                      why we write. It's why The Brown Patience Company exists.
                      To ensure you have all the help you need to share the
                      message you need to share — clearly, compellingly. And
                      then the harvest! We wait for the harvest in the lives of
                      people. For surely, the harvest comes.
                    </p>
                  </div>
                </div>

                {/* Philosophy Image */}
                <div className="flex justify-center md:pt-12 pt-6">
                  <div className=" rounded-lg h-[27.5rem] w-full flex items-center justify-center border-2 border-dashed border-brand-accent/20 ">
                    <img
                      src={b8}
                      alt="Brown Paitence Company"
                      className="h-full w-full object-cover object-top rounded-lg"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20 bg-neutral/50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                What Clients Say
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                Here's what authors and writers have to say about working with
                me on their book projects.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {editingData.slice(0, 6).map((testimonial, index) => (
                <animated.div
                  key={testimonial.id}
                  style={{
                    opacity: 1,
                    transform: "translateY(0px)",
                    transitionDelay: `${index * 100}ms`,
                    transition: "all 0.6s ease-out",
                  }}
                >
                  <Card className="h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4 mb-4">
                        <img
                          src={testimonial.img}
                          alt={testimonial.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                        <div>
                          <h4 className="font-serif font-semibold text-brand-secondary">
                            {testimonial.name}
                          </h4>
                          <div className="flex gap-1">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className="w-4 h-4 fill-brand-accent text-brand-accent"
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                      <blockquote className="text-brand-secondary/70 text-sm leading-relaxed italic">
                        {testimonial.content}
                      </blockquote>
                    </CardContent>
                  </Card>
                </animated.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant max-w-4xl mx-auto text-center">
              <h3 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-6">
                Ready to Share Your Message?
              </h3>
              <p className="text-lg text-brand-secondary/70 mb-8 max-w-2xl mx-auto">
                Let's talk. Tell me about the book you've been desiring to
                write, the message you've been yearning to share. Let me ensure
                you share that message. Together, we will define your audience
                and clarify your message, organize your thoughts into a logical,
                well-flowing outline, and communicate your message clearly,
                compellingly.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="hero"
                  size="lg"
                  onClick={scrollToContact}
                  className="group"
                >
                  Get Started Now
                  <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button variant="outline" size="lg">
                  View More Portfolio
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <Contact
          title="Ready to Start Your Book Project?"
          description="Whether you need editing, ghostwriting, collaboration, or transcription services, I'm here to help you share your message with the world."
          whatsappMessage="Hi Patience! I'm interested in your book writing and editing services. Could we discuss my project?"
        />
      </div>
    </>
  );
};

export default BookWritingEditing;
