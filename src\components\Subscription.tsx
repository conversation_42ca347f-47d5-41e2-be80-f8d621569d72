import { useState } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Check, Star, Mail, Users } from "lucide-react";

interface SubscriptionTier {
  id: string;
  name: string;
  description: string;
  price: string;
  billingPeriod: string;
  features: string[];
  icon: React.ComponentType<{ className?: string }>;
  popular?: boolean;
}

const subscriptionTiers: SubscriptionTier[] = [
  {
    id: "Writing-Compelling-Stories",
    name: " The Art of Writing Compelling Stories",
    description: "A 4-week course that teaches you to tell stories that move.",
    price: "₦25,000",
    billingPeriod: "/month",
    features: [
      "4-week course on writing compelling stories",
      "Learn to write like the writers you admire",
    ],
    icon: Mail,
  },
  {
    id: "solopreneurs",
    name: "Guidance for Writing Solopreneurs",
    description:
      "With this package, you get proofreading, editing, and necessary revisions to your Christian content.",
    price: "₦25,000",
    billingPeriod: "/month",
    features: [
      "Professional proofreading",
      "Content editing services",
      "Necessary revisions included",
      "Christian content focus",
      "Monthly consultation",
    ],
    icon: Users,
    popular: true,
  },
  {
    id: "authors",
    name: "Coaching For Authors",
    description:
      "The coaching package where I guide you as you author your book, chapter by chapter.",
    price: "₦40,000",
    billingPeriod: "/month",
    features: [
      "Chapter-by-chapter guidance",
      "Personalized coaching sessions",
      "Book development support",
      "Publishing roadmap",
      "Direct access to Brown Patience",
    ],
    icon: Star,
  },
];

const Subscription = () => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  const handleSubscribe = async (tierId: string) => {
    setIsSubmitting(true);
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log("Subscribing to:", tierId);
    setIsSubmitting(false);
    alert(`Successfully subscribed!`);
  };

  const handleNewsletterSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;
    await handleSubscribe("free");
    setEmail("");
  };

  return (
    <section id="subscription" ref={ref} className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Star className="w-4 h-4" />
              Join the Community
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Monthly Subscription Packages
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Choose the perfect package to support your writing journey with
              ongoing guidance and professional services.
            </p>
          </div>

          {/* Subscription Tiers */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8 mb-16">
            {subscriptionTiers.map((tier, index) => {
              const IconComponent = tier.icon;

              return (
                <animated.div
                  key={tier.id}
                  style={{
                    opacity: inView ? 1 : 0,
                    transform: inView ? "translateY(0px)" : "translateY(50px)",
                    transitionDelay: inView ? `${300 + index * 150}ms` : "0ms",
                    transition: "all 0.6s ease-out",
                  }}
                >
                  <Card
                    className={`group h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 interactive-lift relative w-full max-w-sm mx-auto lg:max-w-none ${
                      tier.popular
                        ? "ring-2 ring-brand-accent/20 lg:scale-105"
                        : ""
                    }`}
                  >
                    {tier.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge className="bg-brand-accent text-brand-primary">
                          Most Popular
                        </Badge>
                      </div>
                    )}

                    <CardHeader className="text-center p-4 lg:p-6">
                      <div className="mx-auto w-16 h-16 bg-brand-accent/10 rounded-2xl flex items-center justify-center mb-4">
                        <IconComponent className="w-8 h-8 text-brand-accent" />
                      </div>

                      <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                        {tier.name}
                      </h3>

                      <p className="text-brand-secondary/70 leading-relaxed mb-4 text-sm lg:text-base px-2">
                        {tier.description}
                      </p>

                      <div className="text-center">
                        <div className="flex items-end justify-center gap-1">
                          <span className="text-3xl font-bold text-brand-secondary">
                            {tier.price}
                          </span>
                          <span className="text-brand-secondary/60">
                            {tier.billingPeriod}
                          </span>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0 p-4 lg:p-6">
                      <div className="space-y-6">
                        {/* Features */}
                        <div className="space-y-2 lg:space-y-3">
                          {tier.features.map((feature, idx) => (
                            <div
                              key={idx}
                              className="flex items-start gap-2 lg:gap-3"
                            >
                              <Check className="w-4 h-4 lg:w-5 lg:h-5 text-brand-accent flex-shrink-0 mt-0.5" />
                              <span className="text-xs lg:text-sm text-brand-secondary/80 leading-relaxed">
                                {feature}
                              </span>
                            </div>
                          ))}
                        </div>

                        {/* CTA */}
                        <div className="pt-4">
                          {tier.id === "free" ? (
                            <form
                              onSubmit={handleNewsletterSignup}
                              className="space-y-3"
                            >
                              <Input
                                type="email"
                                placeholder="Enter your email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                required
                                className="w-full text-sm"
                              />
                              <Button
                                type="submit"
                                variant="outline"
                                className="w-full group text-sm h-9 lg:h-10"
                                disabled={isSubmitting}
                              >
                                {isSubmitting
                                  ? "Subscribing..."
                                  : "Subscribe Free"}
                                <ArrowRight className="w-3 h-3 lg:w-4 lg:h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                              </Button>
                            </form>
                          ) : (
                            <Button
                              variant={tier.popular ? "hero" : "outline"}
                              className="w-full group text-sm h-9 lg:h-10"
                              onClick={() => handleSubscribe(tier.id)}
                              disabled={isSubmitting}
                            >
                              {isSubmitting
                                ? "Processing..."
                                : `Choose ${tier.name}`}
                              <ArrowRight className="w-3 h-3 lg:w-4 lg:h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </animated.div>
              );
            })}
          </div>

          {/* Money Back Guarantee */}
          <div className="text-center">
            <div className="bg-brand-neutral/50 rounded-2xl p-8 max-w-2xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-4">
                <div className="w-8 h-8 bg-brand-accent/10 rounded-full flex items-center justify-center">
                  <Check className="w-5 h-5 text-brand-accent" />
                </div>
                <h3 className="text-lg font-serif font-bold text-brand-secondary">
                  30-Day Money-Back Guarantee
                </h3>
              </div>
              <p className="text-brand-secondary/70">
                Not satisfied? Get a full refund within 30 days, no questions
                asked.
              </p>
            </div>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default Subscription;
