import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { Toaster } from "@/components/ui/toaster";
import Header from "./layout/Header";
import Footer from "./layout/Footer";
import Home from "./pages/Home";
import About from "./pages/About";
import Books from "./pages/Books";
import BlogList from "./pages/Blog/BlogList";
import Community from "./pages/Community";
import ContactPage from "./pages/Contact";
import GuidanceForSolopreneurs from "./pages/Subscriptions/GuidanceForSolopreneurs";
import CoachingForAuthors from "./pages/Subscriptions/CoachingForAuthors";
import {
  BookWritingEditing,
  CoachingForWriters,
  ContentWriting,
} from "./pages/Services";

function App() {
  return (
    <HelmetProvider>
      <Router>
        <div className="min-h-screen bg-cream-50">
          <Header />
          <main className="pt-24">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/about" element={<About />} />
              <Route path="/books" element={<Books />} />
              <Route path="/blog" element={<BlogList />} />

              <Route
                path="/subscriptions/guidance-for-solopreneurs"
                element={<GuidanceForSolopreneurs />}
              />
              <Route
                path="/subscriptions/coaching-for-authors"
                element={<CoachingForAuthors />}
              />
              <Route path="/community" element={<Community />} />

              {/* Service Pages */}
              <Route
                path="/services/book-writing-editing"
                element={<BookWritingEditing />}
              />
              <Route
                path="/services/coaching-for-writers"
                element={<CoachingForWriters />}
              />
              <Route
                path="/services/content-writing"
                element={<ContentWriting />}
              />

              <Route path="/contact" element={<ContactPage />} />
            </Routes>
          </main>
          <Footer />
          <Toaster />
        </div>
      </Router>
    </HelmetProvider>
  );
}

export default App;
