import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MessageCircle, Calendar, Clock, Phone, Mail } from "lucide-react";

interface ContactProps {
  id?: string;
  title?: string;
  subtitle?: string;
  description?: string;
  whatsappMessage?: string;
  whatsappNumber?: string;
  showPhone?: boolean;
  showEmail?: boolean;
  phoneNumber?: string;
  email?: string;
  className?: string;
  backgroundColor?: string;
}

const Contact = ({
  id = "contact",
  title = "Ready to Start Your Writing Journey?",
  subtitle = "Let's Connect",
  description = "Whether you're just beginning or looking to take your writing to the next level, I'm here to help guide your journey.",
  whatsappMessage = "Hi! I'm interested in learning more about your writing services. Could we schedule a time to chat?",
  whatsappNumber = "2348140170221",
  showPhone = true,
  showEmail = true,
  phoneNumber = "+2348140170221",
  email = "<EMAIL>",
  className = "",
  backgroundColor = "bg-background",
}: ContactProps) => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  const handleWhatsAppClick = () => {
    const message = encodeURIComponent(whatsappMessage);
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    window.open(whatsappUrl, "_blank", "noopener,noreferrer");
  };

  const handleBookingClick = () => {
    // In a real implementation, this would link to a booking system
    console.log("Booking appointment clicked");
  };

  return (
    <section
      id={id}
      ref={ref}
      className={`py-20 ${backgroundColor} ${className}`}
    >
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <MessageCircle className="w-4 h-4" />
              {subtitle}
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              {title}
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              {description}
            </p>
          </div>

          {/* Contact Options */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 max-w-4xl mx-auto mb-12">
            {/* WhatsApp Contact */}
            <Card className="group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 w-full max-w-md mx-auto lg:max-w-none">
              <CardContent className="p-6 lg:p-8 text-center">
                <div className="w-12 h-12 lg:w-16 lg:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 lg:mb-6 group-hover:scale-110 transition-transform duration-300">
                  <MessageCircle className="w-6 h-6 lg:w-8 lg:h-8 text-green-600" />
                </div>

                <h3 className="text-lg lg:text-xl font-serif font-bold text-brand-secondary mb-2 lg:mb-3">
                  Quick Message
                </h3>

                <p className="text-brand-secondary/70 mb-4 lg:mb-6 leading-relaxed text-sm lg:text-base px-2">
                  Have a quick question or want to learn more about my services?
                  Send me a WhatsApp message for a fast, friendly response.
                </p>

                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleWhatsAppClick}
                  className="w-full group/btn border-green-500 text-green-600 hover:bg-green-500 hover:text-white h-11 lg:h-12 text-sm lg:text-base"
                >
                  <MessageCircle className="w-4 h-4 lg:w-5 lg:h-5 mr-2" />
                  Message on WhatsApp
                </Button>
              </CardContent>
            </Card>

            {/* Appointment Booking */}
            <Card className="group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 w-full max-w-md mx-auto lg:max-w-none">
              <CardContent className="p-6 lg:p-8 text-center">
                <div className="w-12 h-12 lg:w-16 lg:h-16 bg-brand-accent/10 rounded-full flex items-center justify-center mx-auto mb-4 lg:mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Calendar className="w-6 h-6 lg:w-8 lg:h-8 text-brand-accent" />
                </div>

                <h3 className="text-lg lg:text-xl font-serif font-bold text-brand-secondary mb-2 lg:mb-3">
                  Book a Consultation
                </h3>

                <p className="text-brand-secondary/70 mb-4 lg:mb-6 leading-relaxed text-sm lg:text-base px-2">
                  Ready for personalized guidance? Schedule a one-on-one
                  consultation to discuss your writing goals and how I can help
                  you achieve them.
                </p>

                <Button
                  variant="hero"
                  size="lg"
                  onClick={handleBookingClick}
                  className="w-full h-11 lg:h-12 text-sm lg:text-base"
                >
                  <Calendar className="w-4 h-4 lg:w-5 lg:h-5 mr-2" />
                  Book an Appointment
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Response Time Notice */}
          <div className="text-center px-4">
            <div className="inline-flex items-center gap-2 bg-brand-neutral/50 px-4 lg:px-6 py-2 lg:py-3 rounded-full text-brand-secondary/70 text-sm lg:text-base">
              <Clock className="w-3 h-3 lg:w-4 lg:h-4" />
              <span className="font-medium">
                Responses typically within 48 hours
              </span>
            </div>
          </div>

          {/* Additional Contact Info */}
          {(showPhone || showEmail) && (
            <div className="mt-12 lg:mt-16 text-center px-4">
              <h3 className="text-base lg:text-lg font-serif font-semibold text-brand-secondary mb-3 lg:mb-4">
                Prefer a Different Way to Connect?
              </h3>
              <div className="flex flex-col gap-3 lg:gap-6 lg:flex-row justify-center items-center text-brand-secondary/70 text-sm lg:text-base">
                {showPhone && (
                  <a
                    href={`tel:${phoneNumber}`}
                    className="flex items-center gap-2 hover:text-brand-accent transition-colors duration-200 p-2 rounded-lg hover:bg-brand-neutral/20"
                  >
                    <Phone className="w-3 h-3 lg:w-4 lg:h-4" />
                    <span>{phoneNumber}</span>
                  </a>
                )}
                {showPhone && showEmail && (
                  <div className="hidden lg:block w-1 h-1 bg-brand-secondary/30 rounded-full"></div>
                )}
                {showEmail && (
                  <a
                    href={`mailto:${email}`}
                    className="flex items-center gap-2 hover:text-brand-accent transition-colors duration-200 p-2 rounded-lg hover:bg-brand-neutral/20 break-all lg:break-normal"
                  >
                    <Mail className="w-3 h-3 lg:w-4 lg:h-4 flex-shrink-0" />
                    <span className="text-xs lg:text-sm">{email}</span>
                  </a>
                )}
              </div>
            </div>
          )}
        </animated.div>
      </div>
    </section>
  );
};

export default Contact;
