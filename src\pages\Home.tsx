import { useEffect } from "react";
import "animate.css";

import MetaTags from "@/components/MetaTags";
import Hero from "@/components/Hero";
import FeaturedBooks from "@/components/FeaturedBooks";
import AboutSnapshot from "@/components/AboutSnapshot";
import Services from "@/components/Services";
import MyWorks from "@/components/MyWorks";
// import Newsletter from "@/components/Newsletter";
import Subscription from "@/components/Subscription";
import BlogPosts from "@/components/BlogPosts";
import Testimonials from "@/components/Testimonials";
import Contact from "@/components/Contact";

const Home = () => {
  useEffect(() => {
    // Check for reduced motion preference and apply class if needed
    const hasReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;
    const storedPreference = localStorage.getItem("reducedMotion");

    if (storedPreference === "true" || hasReducedMotion) {
      document.documentElement.classList.add("reduced-motion");
    }

    // Add skip-to-content functionality for accessibility
    const skipLink = document.createElement("a");
    skipLink.href = "#main-content";
    skipLink.textContent = "Skip to main content";
    skipLink.className =
      "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-brand-accent focus:text-white focus:rounded-md";
    document.body.insertBefore(skipLink, document.body.firstChild);

    // Cleanup
    return () => {
      if (document.body.contains(skipLink)) {
        document.body.removeChild(skipLink);
      }
    };
  }, []);

  return (
    <>
      <MetaTags
        title="Brown Patience - Author & Writing Coach"
        description="Get the help you need to write your book, to share your message — clearly, compellingly. Professional writing and editing services."
        image="/og-images/home.webp"
        url="https://thebrownpatiencecompany.com.ng"
        keywords="writing coach, author, publishing, book writing, editorial services, creative writing, Brown Patience"
      />
      <div className="min-h-screen bg-background text-foreground">
        {/* Main Content */}
        <main id="main-content">
          {/* Hero Section */}
          <Hero />
          {/* Services */}
          <Services />
          {/* About Snapshot */}
          <AboutSnapshot />

          {/* Featured Books */}
          <FeaturedBooks />
          {/* Subscription Plans */}
          <Subscription />

          {/* My Works Portfolio */}
          <MyWorks />
          {/* Testimonials */}
          <Testimonials />

          {/* Newsletter Capture */}
          {/* <Newsletter /> */}

          {/* Latest Blog Posts */}
          <BlogPosts />

          {/* Contact Section */}
          <Contact />
        </main>
      </div>
    </>
  );
};

export default Home;
